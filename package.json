{"name": "a2svhub-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.6.0", "@radix-ui/react-select": "^2.2.5", "@reduxjs/toolkit": "^2.2.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "immer": "^10.1.1", "lucide-react": "^0.511.0", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.0.0", "react-redux": "^9.1.0", "recharts": "^2.15.4", "sass": "^1.89.1", "sharp": "^0.34.3", "redux-thunk": "^3.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^5"}}