const headItems = [
  {
    name: "Take Attendance",
    href: "/take-attendance",
    icon: "/take-attendance.svg",
  },
  {
    name: "Add Problems",
    href: "/add-problems",
    icon: "/add-icon.svg",
  },
  {
    name: "Daily Problems",
    href: "/daily-problems",
    icon: "/daily-problems.svg",
  },
  {
    name: "Add Contest",
    href: "/add-contest",
    icon: "/add-icon.svg",
  },
  {
    name: "Add Track",
    href: "/add-track",
    icon: "/add-icon.svg",
  },
  {
    name: "Add Exercise",
    href: "/add-exercise",
    icon: "/add-icon.svg",
  },

  {
    name: "Add Session",
    href: "/add-session",
    icon: "/add-icon.svg",
  },

  {
    name: "Stipends Funds",
    href: "/stipends-funds",
    icon: "/stipends-funds.svg",
  },
];

export default function SidebarHeadSection({
  effectivelyExpanded,
  pathname,
  collapsed,
}: {
  effectivelyExpanded: boolean;
  pathname: string;
  collapsed: boolean;
}) {
  return (
    <>
      {effectivelyExpanded && (
        <p className="uppercase text-xs items-center pt-4 pl-5 font-bold leading-tight tracking-wide">
          head
        </p>
      )}
      <nav className="flex flex-col py-4">
        {headItems.map((item) => (
          <div
            key={item.name}
            className={`relative hover:bg-gray-100 text-gray-600 rounded-md my-1 ${
              pathname === item.href
                ? "bg-[rgb(0,171,85)]/10 text-[rgb(0,171,85)]"
                : ""
            }`}
          >
            <a
              href={item.href}
              className={`flex items-center gap-3 p-3 pl-2 mx-3 rounded-lg text-gray-700 transition-all duration-200 ${
                !effectivelyExpanded ? "justify-center" : ""
              }`}
            >
              <div className="w-5 h-5 flex-shrink-0">
                <img
                  src={item.icon}
                  alt={item.name + " icon"}
                  className="w-5 h-5"
                  style={{
                    filter:
                      pathname === item.href
                        ? "invert(41%) sepia(98%) saturate(749%) hue-rotate(86deg) brightness(97%) contrast(101%)"
                        : "invert(34%) sepia(6%) saturate(0%) hue-rotate(175deg) brightness(95%) contrast(87%)",
                  }}
                />
              </div>
              {effectivelyExpanded && (
                <span
                  className={`whitespace-nowrap overflow-hidden text-sm items-center ${
                    pathname === item.href
                      ? "text-[rgb(0,171,85)]"
                      : "text-gray-600"
                  }`}
                >
                  {item.name}
                </span>
              )}
            </a>
          </div>
        ))}
      </nav>
    </>
  );
}
