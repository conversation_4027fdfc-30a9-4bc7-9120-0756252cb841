"use client";
import { useState } from "react";
import { usePathname } from "next/navigation";
import SidebarHeader from "./sidebarHeader";
import SidebarNavSection from "./sidebarNavSection";
import SidebarFooter from "./sidebarFooter";
import SidebarHeadSection from "./sidebarHeadSection";

export default function Sidebar({ role }: { role?: string }) {
  const pathname = usePathname();
  const [collapsed, setCollapsed] = useState(false);
  const [hovering, setHovering] = useState(false);
  const [openSubmenu, setOpenSubmenu] = useState<string | null>(null);
  const effectivelyExpanded = !collapsed || (collapsed && hovering);

  return (
    <aside
      className={` sidebar-scrollbar  flex flex-col transition-all duration-300 ease-in-out pl-3 pr-3 overflow-y-auto scrollbar-hide flex-shrink-0 relative border-gray-300 border-r border-dashed ${
        effectivelyExpanded ? "w-64" : "w-25"
      } ${hovering ? "bg-white/90 backdrop-blur-md" : "bg-white"}`}
      onMouseEnter={() => setHovering(true)}
      onMouseLeave={() => setHovering(false)}
    >
      <SidebarHeader
        effectivelyExpanded={effectivelyExpanded}
        collapsed={collapsed}
        hovering={hovering}
        setCollapsed={setCollapsed}
      />
      <SidebarNavSection
        effectivelyExpanded={effectivelyExpanded}
        pathname={pathname}
        openSubmenu={openSubmenu}
        setOpenSubmenu={setOpenSubmenu}
        hovering={hovering}
        collapsed={collapsed}
      />
      {role === "head" && (
        <SidebarHeadSection
          effectivelyExpanded={effectivelyExpanded}
          collapsed={collapsed}
          pathname={pathname}
        />
      )}
      <SidebarFooter effectivelyExpanded={effectivelyExpanded} />
    </aside>
  );
}
