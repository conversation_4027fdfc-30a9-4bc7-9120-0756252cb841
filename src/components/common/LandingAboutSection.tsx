"use client";
import * as React from "react";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import Typography from "@mui/material/Typography";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

const faqData = [
    {
        question: "Who can use A2SV Hub?",
        answer: "To access the A2SV Hub, participation in the A2SV Education program is a prerequisite. Our platform is purposefully designed as an internal tool, tailored exclusively for educating our students within the A2SV Education framework. Currently, it is not intended for public use. As we prioritize the needs and progress of our enrolled students, we continuously refine and enhance the A2SV Hub to meet the evolving demands of our educational initiatives.",
    },
    {
        question: "How to join A2SV?",
        answer: "To join A2SV, simply visit our official website at a2sv.org. While A2SV is open to everyone, it particularly welcomes university students. Explore our website to learn more about our mission, programs, and opportunities for involvement. We look forward to welcoming you to our community!",
    },
    {
        question: "How to register for A2SV Hub?",
        answer: "If you’re a member of A2SV, expect to receive an invitation directly to your email inbox. Should you find yourself without access, kindly reach out to your department heads for assistance. Your prompt communication ensures swift resolution, granting you entry to our exclusive educational platform, the A2SV Hub, where learning and collaboration thrive.",
    },
    {
        question: "How does the hub work?",
        answer: "At the core of the A2SV Hub lies a powerful relational database that serves as the foundation for organizing our data. Leveraging this structure, we develop various features and automations tailored to streamline your experience. These functionalities are meticulously crafted to simplify tasks, enhance efficiency, and ultimately make your educational journey smoother. By harnessing the capabilities of our database and layering on intelligent automations, the A2SV Hub empowers you to navigate your academic endeavors with ease and precision.",
    },
];

export default function LandingAboutSection() {
    return (
        <section className="bg-[#FCFBF7] py-16 px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
                <div className="text-center mb-12">
                    <h2
                        className="font-bold text-3xl"
                        style={{ fontSize: "32px" }}
                    >
                        About{" "}
                        <span className="text-green-600 drop-shadow-lg">
                            A2SV
                        </span>
                    </h2>
                </div>
                <p className="text-center  mb-16">
                    African to Silicon Valley is a non-profit organization
                    committed to educating high-potential university students in
                    Africa. We establish valuable partnerships with top tech
                    companies like <span className="font-bold">Google</span>,{" "}
                    <span className="font-bold">Palantir</span>,{" "}
                    <span className="font-bold">Databricks</span>,{" "}
                    <span className="font-bold">Bloomberg</span>, and{" "}
                    <span className="font-bold">Meta</span>, offering our
                    students opportunities in Silicon Valley. Our free program
                    equips students with problem solving skills and encourages
                    them to address critical issues in their home countries
                    using digital solutions. By removing financial barriers, we
                    ensure that talent is not only recognized but also nurtured,
                    promoting a pathway to success.
                </p>

                <div className="text-center mb-12">
                    <h2 className="font-bold text-3xl">FAQ</h2>
                </div>

                <div className="rounded-2xl">
                    {faqData.map((faq, index) => (
                        <Accordion
                            key={index}
                            disableGutters
                            elevation={0}
                            sx={{
                                backgroundColor: "FFFFFF",
                                "&:before": {
                                    display: "none",
                                },
                                "&.Mui-expanded": {
                                    margin: "1rem 0",
                                    borderRadius: "8px",
                                    border: "1px solid #E5E7EB",
                                    backgroundColor: "#FFFFFF",
                                    boxShadow: "0 4px 12px 0 rgba(0,0,0,0.05)",
                                },
                                borderBottom: "1px solid #E5E7EB",
                            }}
                        >
                            <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls={`panel${index}a-content`}
                                id={`panel${index}a-header`}
                            >
                                <Typography
                                    component="span"
                                    sx={{ fontWeight: "500" }}
                                >
                                    {faq.question}
                                </Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Typography sx={{ color: "text.secondary" }}>
                                    {faq.answer}
                                </Typography>
                            </AccordionDetails>
                        </Accordion>
                    ))}
                </div>
            </div>
        </section>
    );
}
