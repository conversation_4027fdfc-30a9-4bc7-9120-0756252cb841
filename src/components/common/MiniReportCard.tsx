import React from "react";
import Image from "next/image";

interface MiniReportCardProps {
    title: string;
    totalCount: number;
    currentValue: number;
    unit?: string;
    customFormatting?: boolean;
    icon?: string;
    showChart?: boolean;
}

const MiniReportCard: React.FC<MiniReportCardProps> = ({
    title,
    totalCount,
    currentValue,
    unit,
    customFormatting = false,
    icon,
    showChart = false,
}) => {
    const percentage =
        totalCount > 0 ? ((currentValue / totalCount) * 100).toFixed(1) : "0.0";

    const formatTimeSpent = (minutes: number) => {
        const days = Math.floor(minutes / (24 * 60));
        return {
            displayValue: `${minutes.toLocaleString()}`,
            subtitle: `That's ${days} Day${days !== 1 ? "s" : ""}`,
        };
    };

    const timeFormatted = customFormatting
        ? formatTimeSpent(currentValue)
        : null;

    const formatDisplayValue = () => {
        if (customFormatting && timeFormatted) {
            return timeFormatted.displayValue;
        }
        return currentValue.toLocaleString();
    };

    return (
        <div className="bg-white rounded-2xl p-6 flex flex-col items-start relative">
            <div className="flex flex-col items-start justify-between mb-4">
                <div>
                    <h3 className="text-md font-medium mb-2">{title}</h3>
                </div>
                <div className="flex items-center gap-2">
                    <Image
                        src="/icons/arrow-home.svg"
                        alt="trend"
                        width={16}
                        height={16}
                        className="w-6 h-6 p-1 bg-green-200 rounded-full"
                    />
                    <span className="text-sm font-medium">{percentage}%</span>
                </div>
            </div>

            <div className="mb-2">
                <div className="flex items-baseline gap-1">
                    <span className="text-3xl font-bold text-gray-900">
                        {formatDisplayValue()}
                    </span>
                    {unit && (
                        <span className="text-sm text-gray-500">({unit})</span>
                    )}
                </div>

                {customFormatting && timeFormatted && (
                    <p className="text-[0.75rem] text-gray-500">
                        {timeFormatted.subtitle}
                    </p>
                )}
            </div>

            {showChart && icon && (
                <div className="mt-4 absolute right-10 top-1/3">
                    <Image
                        src={icon}
                        alt={`${title} chart`}
                        width={200}
                        height={32}
                        className="w-full h-12 object-contain"
                    />
                </div>
            )}

            {!showChart && icon && (
                <div className="mt-4">
                    <Image
                        src={icon}
                        alt={title}
                        width={24}
                        height={24}
                        className="w-6 h-6"
                    />
                </div>
            )}
        </div>
    );
};

export default MiniReportCard;
