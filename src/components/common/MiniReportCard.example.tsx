import React from "react";
import MiniReportCard from "./MiniReportCard";

// Example usage of the MiniReportCard component
const MiniReportCardExample: React.FC = () => {
    return (
        <div className="p-8 bg-gray-50 min-h-screen">
            <h1 className="text-2xl font-bold mb-8">MiniReportCard Examples</h1>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl">
                {/* Solutions Card - matches the design exactly */}
                <MiniReportCard
                    title="Solutions"
                    totalCount={100000} // Assuming a large total to get 0.0%
                    currentValue={501}
                />

                {/* Time Spent Card - matches the design exactly */}
                <MiniReportCard
                    title="Time Spent"
                    totalCount={100000} // Assuming a large total to get 0.0%
                    currentValue={10925}
                    unit="min"
                    customFormatting={true}
                />

                {/* Rating Card with Chart - matches the design exactly */}
                <MiniReportCard
                    title="Rating"
                    totalCount={100000} // Assuming a large total to get 0.0%
                    currentValue={1636}
                    icon="/icons/bar-home.svg"
                    showChart={true}
                />
            </div>

            <div className="mt-12">
                <h2 className="text-xl font-semibold mb-4">Component Props:</h2>
                <div className="bg-white p-6 rounded-lg border">
                    <pre className="text-sm text-gray-700">
                        {`interface MiniReportCardProps {
  title: string;           // Card title (e.g., "Solutions", "Time Spent", "Rating")
  totalCount: number;      // Total count for percentage calculation
  currentValue: number;    // Current user value
  unit?: string;          // Optional unit display (e.g., "min")
  customFormatting?: boolean; // Enable time formatting (minutes + days)
  icon?: string;          // Optional icon/chart SVG path
  showChart?: boolean;    // Display as chart (for rating card)
}`}
                    </pre>
                </div>
            </div>
        </div>
    );
};

export default MiniReportCardExample;
