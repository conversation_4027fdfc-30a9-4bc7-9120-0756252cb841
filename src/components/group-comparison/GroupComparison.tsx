"use client";

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import './GroupComparison.scss';
import Dropdown from '../common/Dropdown';
import GroupSelector from './GroupSelector';
import DataTable from './DataTable';
import Graph from './Graph';

interface GroupData {
  id: string;
  name: string;
  consistency: number;
  trackCompletion: number;
  realAttendance: number;
  totalAttendance: number;
  submissionsCount: number;
  contestRating: number;
  realAttendancePercentage: number;
  totalAttendancePercentage: number;
  present: number;
  excused: number;
  absent: number;
  checkInOutRatio: number;
  averageConsistency: number;
  minimumConsistency: number;
  maximumConsistency: number;
  averageDailySolve: number;
  totalExpectedSolves: number;
  actualSolves: number;
}

type TabType = 'summary' | 'attendance' | 'consistency' | 'trackCompletion' | 'contestPerformance';


const TABLE_OPTIONS = [
  { value: 'Table', label: 'Table' },
  { value: 'Chart', label: 'Graph' },
];

const TIME_RANGE_OPTIONS = [
  { value: 'All Time', label: 'All Time' },
  { value: 'Last Week', label: 'Last Week' },
  { value: 'This Month', label: 'This Month' },
  { value: 'Last Month', label: 'Last Month' },
  { value: 'Last 7 days', label: 'Last 7 days' },
  { value: 'Last 30 days', label: 'Last 30 days' },
  { value: 'Last 60 days', label: 'Last 60 days' },
  { value: 'Pick custom date range...', label: 'Pick custom date range...' },
];

const TABS = [
  { id: 'summary', label: 'Summary' },
  { id: 'attendance', label: 'Attendance' },
  { id: 'consistency', label: 'Consistency' },
  { id: 'trackCompletion', label: 'Track Completion' },
  { id: 'contestPerformance', label: 'Contest Performance' },
] as const;

const TIME_RANGE_MULTIPLIERS = {
  'Last Week': 0.8,
  'This Month': 0.9,
  'Last Month': 0.7,
  'Last 7 days': 0.6,
  'Last 30 days': 0.85,
  'Last 60 days': 0.95,
} as const;


const MOCK_GROUP_DATA: GroupData[] = [
    {
      id: 'group1',
      name: 'A2SV AAIT Group 12',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 0,
      contestRating: 1400,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 0,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 0,
      actualSolves: 0,
    },
    {
      id: 'group2',
      name: 'A2SV AAIT Group 31',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 0,
      contestRating: 1400,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 0,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 0,
      actualSolves: 0,
    },
    {
      id: 'group3',
      name: 'A2SV AAIT Group 32',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 0,
      contestRating: 1400,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 0,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 0,
      actualSolves: 0,
    },
    {
      id: 'group4',
      name: 'A2SV AAIT Group 42',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 13,
      contestRating: 1389.6,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 100,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 13,
      actualSolves: 1,
    },
    {
      id: 'group5',
      name: 'A2SV AAIT Group 43',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 12,
      contestRating: 1396.6,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 100,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 13,
      actualSolves: 1,
    },
    {
      id: 'group6',
      name: 'A2SV AAIT Group 44',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 0,
      contestRating: 1400,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 0,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 0,
      actualSolves: 0,
    },
    {
      id: 'group7',
      name: 'A2SV AASTU Group 33',
      consistency: 0,
      trackCompletion: 0,
      realAttendance: 0,
      totalAttendance: 0,
      submissionsCount: 0,
      contestRating: 1400,
      realAttendancePercentage: 0,
      totalAttendancePercentage: 0,
      present: 0,
      excused: 0,
      absent: 0,
      checkInOutRatio: 0,
      averageConsistency: 0,
      minimumConsistency: 0,
      maximumConsistency: 0,
      averageDailySolve: 0,
      totalExpectedSolves: 0,
      actualSolves: 0,
    }
  ];

const GROUP_OPTIONS = MOCK_GROUP_DATA.map(group => ({
  value: group.id,
  label: group.name,
}));

// Utility functions
const getRankDisplay = (index: number) => {
  const rankIcons = ['🏆', '🥈', '🥉'];
  return index < 3 ? (
    <span className={`rank-icon ${['gold', 'silver', 'bronze'][index]}`}>{rankIcons[index]}</span>
  ) : (
    <span className="rank-text">{index + 1}th</span>
  );
};


const GroupComparison = () => {

  const [activeTab, setActiveTab] = useState<TabType>('summary');
  const [selectedGroups, setSelectedGroups] = useState<string[]>(['group1', 'group2', 'group4', 'group5', 'group6']);
  const [tableType, setTableType] = useState<string | number>('Table');
  const [timeRange, setTimeRange] = useState<string | number>('All Time');
  const [isGroupDropdownOpen, setIsGroupDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);


  const handleGroupToggle = useCallback((groupId: string) => {
    setSelectedGroups(prev => 
      prev.includes(groupId) 
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  }, []);

  const handleClearAll = useCallback(() => {
    setSelectedGroups([]);
  }, []);

  const handleDropdownToggle = useCallback(() => {
    setIsGroupDropdownOpen(prev => !prev);
  }, []);

  const filteredGroupData = useMemo(() => {
    let filteredData = MOCK_GROUP_DATA.filter(group => selectedGroups.includes(group.id));
    
    const multiplier = TIME_RANGE_MULTIPLIERS[timeRange as keyof typeof TIME_RANGE_MULTIPLIERS];
    if (multiplier) {
        filteredData = filteredData.map(group => ({
          ...group,
        consistency: Math.round(group.consistency * multiplier),
        trackCompletion: Math.round(group.trackCompletion * multiplier),
        realAttendance: Math.round(group.realAttendance * multiplier),
        totalAttendance: Math.round(group.totalAttendance * multiplier),
        submissionsCount: Math.round(group.submissionsCount * multiplier),
        contestRating: Math.round(group.contestRating * multiplier),
      }));
    }
    
    return filteredData;
  }, [selectedGroups, timeRange]);

  const sortedGroupData = useMemo(() => 
    [...filteredGroupData].sort((a, b) => b.consistency - a.consistency),
    [filteredGroupData]
  );

  const tableConfigs = useMemo(() => ({
    summary: {
      columns: [
        { key: 'consistency' as keyof GroupData, label: 'Consistency', thresholds: { green: 80, yellow: 60 } },
        { key: 'trackCompletion' as keyof GroupData, label: 'Track Completion', thresholds: { green: 80, yellow: 60 } },
        { key: 'realAttendance' as keyof GroupData, label: 'Real Attendance', thresholds: { green: 1000, yellow: 500 } },
        { key: 'totalAttendance' as keyof GroupData, label: 'Total Attendance', thresholds: { green: 1000, yellow: 500 } },
        { key: 'submissionsCount' as keyof GroupData, label: 'Submissions count', thresholds: { green: 2000, yellow: 1000 } },
        { key: 'contestRating' as keyof GroupData, label: 'Contest Rating', thresholds: { green: 1400, yellow: 1200 } },
      ]
    },
    attendance: {
      columns: [
        { 
          key: 'realAttendancePercentage' as keyof GroupData, 
          label: 'Real Attendance percentage', 
          thresholds: { green: 90, yellow: 80 },
          formatter: (value: number) => (
            <>
              {value}
              {value < 85 && <span className="percentage-indicator">&lt;85%</span>}
            </>
          )
        },
        { key: 'totalAttendancePercentage' as keyof GroupData, label: 'Total Attendance percentage', thresholds: { green: 90, yellow: 80 } },
        { key: 'realAttendance' as keyof GroupData, label: 'Real Attendance', thresholds: { green: 1000, yellow: 500 } },
        { key: 'totalAttendance' as keyof GroupData, label: 'Total Attendance', thresholds: { green: 1000, yellow: 500 } },
        { key: 'present' as keyof GroupData, label: 'Present', thresholds: { green: 1000, yellow: 500 } },
        { key: 'excused' as keyof GroupData, label: 'Excused', thresholds: { green: 50, yellow: 20 } },
        { key: 'absent' as keyof GroupData, label: 'Absent', thresholds: { green: 10, yellow: 20 }, reverse: true },
        { key: 'checkInOutRatio' as keyof GroupData, label: 'Check In / Check Out ratio', thresholds: { green: 1.0, yellow: 0.8 } },
      ]
    },
    consistency: {
      columns: [
        { key: 'averageConsistency' as keyof GroupData, label: 'Average Consistency', thresholds: { green: 80, yellow: 60 } },
        { key: 'minimumConsistency' as keyof GroupData, label: 'Minimum Consistency', thresholds: { green: 70, yellow: 50 } },
        { key: 'maximumConsistency' as keyof GroupData, label: 'Maximum Consistency', thresholds: { green: 90, yellow: 80 } },
        { key: 'averageDailySolve' as keyof GroupData, label: 'Average Daily Solve per Student', thresholds: { green: 2.5, yellow: 1.5 } },
        { key: 'totalExpectedSolves' as keyof GroupData, label: 'Total Expected Solves', thresholds: { green: 1500, yellow: 1000 } },
        { key: 'actualSolves' as keyof GroupData, label: 'Actual Solves', thresholds: { green: 1500, yellow: 1000 } },
      ]
    },
    trackCompletion: {
      columns: [
        { key: 'trackCompletion' as keyof GroupData, label: 'Average Track Completion', thresholds: { green: 75, yellow: 50 } },
        { key: 'averageDailySolve' as keyof GroupData, label: 'Average Exercises per Student', thresholds: { green: 10, yellow: 5 } },
        { key: 'totalExpectedSolves' as keyof GroupData, label: 'Exercises', thresholds: { green: 20, yellow: 10 } },
        { key: 'actualSolves' as keyof GroupData, label: 'Tracks', thresholds: { green: 5, yellow: 2 } },
        { key: 'consistency' as keyof GroupData, label: 'Completion percentage', thresholds: { green: 75, yellow: 50 } },
        { key: 'minimumConsistency' as keyof GroupData, label: 'Minimum Completion percentage', thresholds: { green: 75, yellow: 50 } },
        { key: 'maximumConsistency' as keyof GroupData, label: 'Maximum Completion percentage', thresholds: { green: 75, yellow: 50 } },
        { key: 'averageConsistency' as keyof GroupData, label: 'Good >75%', thresholds: { green: 10, yellow: 5 } },
        { key: 'realAttendance' as keyof GroupData, label: 'Okay >50%', thresholds: { green: 10, yellow: 5 } },
        { key: 'totalAttendance' as keyof GroupData, label: 'Bad <50%', thresholds: { green: 10, yellow: 5 } },
        { key: 'submissionsCount' as keyof GroupData, label: 'Students Who Completed the track', thresholds: { green: 20, yellow: 10 } },
      ]
    }
  }), []);

  // Dropdown close handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsGroupDropdownOpen(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isGroupDropdownOpen) {
        setIsGroupDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('click', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isGroupDropdownOpen]);

  // Render table content
  const renderTableContent = () => {
    if (tableType === 'Chart') {
      return (
        <Graph 
          data={sortedGroupData} 
          activeTab={activeTab}
        />
      );
    }

    const config = tableConfigs[activeTab as keyof typeof tableConfigs];
    
    if (config) {
      return (
        <DataTable 
          data={sortedGroupData} 
          columns={config.columns}
          getRankDisplay={getRankDisplay}
          getRowKey={(item) => item.id}
          getRowName={(item) => item.name}
        />
      );
    }
    
    return <div className="placeholder">{activeTab} data will be displayed here</div>;
  };

  return (
    <div className="group-comparison">
      {/* Header */}
      <div className="header">
        <div className="header-content">
          <div className="title-section">
            <h1 className="title">Group Comparison (BETA)</h1>
            <p className="subtitle">
              Data will update in 5h 23m 22s
            </p>
          </div>
          <div className="group-selector">
            <GroupSelector
              selectedGroups={selectedGroups}
              onGroupToggle={handleGroupToggle}
              onClearAll={handleClearAll}
              isOpen={isGroupDropdownOpen}
              onToggle={handleDropdownToggle}
              dropdownRef={dropdownRef}
              groupOptions={GROUP_OPTIONS}
            />
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="filters">
        <Dropdown
          value={tableType}
          onChange={(value) => setTableType(value.toString())}
          options={TABLE_OPTIONS}
        />
        <Dropdown
          value={timeRange}
          onChange={(value) => setTimeRange(value.toString())}
          options={TIME_RANGE_OPTIONS}
        />
      </div>

      {/* Tabs */}
      <div className="tabs">
        {TABS.map((tab) => (
          <button
            key={tab.id}
            className={`tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as TabType)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Table Content */}
      <div className="table-content">
        {renderTableContent()}
      </div>
    </div>
  );
};

export default GroupComparison; 