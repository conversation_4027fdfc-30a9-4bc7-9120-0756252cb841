import React from 'react';
import './GroupSelector.scss';

interface GroupOption {
  value: string;
  label: string;
}

interface GroupSelectorProps {
  selectedGroups: string[];
  onGroupToggle: (groupId: string) => void;
  onClearAll: () => void;
  isOpen: boolean;
  onToggle: () => void;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
  groupOptions: GroupOption[];
}

const GroupSelector: React.FC<GroupSelectorProps> = ({
  selectedGroups,
  onGroupToggle,
  onClearAll,
  isOpen,
  onToggle,
  dropdownRef,
  groupOptions,
}) => (
  <div className="multi-select-dropdown" ref={dropdownRef}>
    <button
      className="multi-select-trigger"
      onClick={(e) => {
        e.stopPropagation();
        onToggle();
      }}
    >
      <span className="selected-text">
        {selectedGroups.length > 0 
          ? `${selectedGroups.length} Groups selected`
          : 'Select Groups'
        }
      </span>
      {selectedGroups.length > 0 && (
        <button
          className="clear-button"
          onClick={(e) => {
            e.stopPropagation();
            onClearAll();
          }}
        >
          ×
        </button>
      )}
      <span className="dropdown-arrow">
        {isOpen ? '▲' : '▼'}
      </span>
    </button>
    
    {isOpen && (
      <div className="multi-select-dropdown-menu">
        {groupOptions.map((option) => (
          <div
            key={option.value}
            className={`multi-select-option ${
              selectedGroups.includes(option.value) ? 'selected' : ''
            }`}
            onClick={(e) => {
              e.stopPropagation();
              onGroupToggle(option.value);
            }}
          >
            <div className="checkbox">
              <span className="checkmark"></span>
            </div>
            <span className="option-label">{option.label}</span>
          </div>
        ))}
      </div>
    )}
  </div>
);

export default GroupSelector; 