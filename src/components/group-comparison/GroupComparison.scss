.group-comparison {
  width: 100%;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 2rem;

  .header {
    margin-bottom: 2rem;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 2rem;

      .title-section {
        flex: 1;

        .title {
          font-size: 2rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0 0 0.5rem 0;
        }

        .subtitle {
          font-size: 1rem;
          color: #6b7280;
          margin: 0;

          .time-filter-note {
            color: #059669;
            font-weight: 500;
          }
        }
      }

      .group-selector {
        min-width: 300px;
      }
    }
  }

  .filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;

    .relative {
      min-width: 300px;
      z-index: 10000!important;
      
    }
  }

  .tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 2rem;

    .tab {
      padding: 1rem 1.5rem;
      background: none;
      border: none;
      font-size: 0.875rem;
      font-weight: 500;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        color: #374151;
        border-bottom-color: #d1d5db;
      }

      &.active {
        color: #059669;
        border-bottom-color: #059669;
      }
    }
  }

  .table-content {
    .placeholder {
      padding: 3rem;
      text-align: center;
      color: #6b7280;
      font-size: 1rem;
      background: #f9fafb;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }
  }
}

// Responsive styles
@media (max-width: 1024px) {
  .group-comparison {
    padding: 1.5rem;

    .header {
      .header-content {
        flex-direction: column;
        gap: 1rem;

        .group-selector {
          min-width: 100%;
        }
      }
    }

    .filters {
      flex-direction: column;
      gap: 0.75rem;


      .relative {
        min-width: 100%;
      }
    }

    .tabs {
      overflow-x: auto;
      
      .tab {
        padding: 0.75rem 1rem;
        white-space: nowrap;
      }
    }


  }
}

@media (max-width: 768px) {
  .group-comparison {
    padding: 1rem;

    .header {
      .header-content {
        .title-section {
          .title {
            font-size: 1.5rem;
          }
        }
      }
    }

    .tabs {
      .tab {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
      }
    }


  }
}

@media (max-width: 480px) {
  .group-comparison {
    .header {
      .header-content {
        .title-section {
          .title {
            font-size: 1.25rem;
          }
        }
      }
    }

    .tabs {
      .tab {
        padding: 0.5rem;
        font-size: 0.75rem;
      }
    }
  }
} 