.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;

    th, td {
      padding: 12px 16px;
      text-align: left;
      border-bottom: 1px solid #f3f4f6;
      white-space: nowrap;
    }

    th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #374151;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    tr {
      transition: background-color 0.2s ease;
      background: white;

      &:hover {
        background-color: #f9fafb;
      }

      &:nth-child(even) {
        background: #fafbfc;
        
        &:hover {
          background-color: #f9fafb;
        }
      }

      &:last-child td {
        border-bottom: none;
      }
    }

    .group-name {
      font-weight: 500;
      color: #1f2937;
      display: flex;
      align-items: center;
      gap: 8px;

      .rank-icon {
        font-size: 16px;
        
        &.gold {
          color: #fbbf24;
        }
        
        &.silver {
          color: #9ca3af;
        }
        
        &.bronze {
          color: #d97706;
        }
      }

      .rank-text {
        font-size: 14px;
        color: #6b7280;
        font-weight: 400;
        min-width: 30px;
      }
    }

    // Simple highlight classes matching the image
    .highlight-green {
      color: #059669;
      font-weight: 500;
    }

    .highlight-yellow {
      color: #d97706;
      font-weight: 500;
      text-decoration: underline;
      text-decoration-color: #fbbf24;
      text-underline-offset: 2px;
    }

    .highlight-red {
      color: #059669;
      font-weight: 500;
      text-decoration: underline;
      text-decoration-color: #dc2626;
      text-underline-offset: 2px;
    }

    // Percentage indicator
    .percentage-indicator {
      font-size: 12px;
      color: #6b7280;
      font-weight: 400;
      margin-left: 4px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .table-container {
    .data-table {
      font-size: 13px;

      th, td {
        padding: 8px 12px;
      }

      .group-name {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }
  }
} 