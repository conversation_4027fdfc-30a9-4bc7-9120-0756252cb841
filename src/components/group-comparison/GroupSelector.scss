.multi-select-dropdown {
  position: relative;
  width: 100%;
  max-width: 300px;

  .multi-select-trigger {
    width: 100%;
    padding: 12px 16px;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-size: 14px;
    transition: border-color 0.2s ease;

    &:hover {
      border-color: #059669;
    }

    &:focus {
      outline: none;
      border-color: #059669;
      box-shadow: 0 0 0 2px rgba(5, 150, 105, 0.25);
    }

    .selected-text {
      flex: 1;
      text-align: left;
      color: #333;
      font-weight: 500;
    }

    .clear-button {
      background: none;
      border: none;
      color: #666;
      font-size: 18px;
      cursor: pointer;
      padding: 0 8px;
      margin-right: 8px;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
        color: #333;
      }
    }

    .dropdown-arrow {
      color: #666;
      font-size: 12px;
      transition: transform 0.2s ease;
    }
  }

  .multi-select-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 25000;
    max-height: 300px;
    overflow-y: auto;
    margin-top: 4px;

    .multi-select-option {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      border-bottom: 1px solid #f8f9fa;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f8f9fa;
      }

      &.selected {
        background-color: #f0fdf4;

        .checkbox {
          background-color: #059669;
          border-color: #059669;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 2px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
          }
        }
      }

      .checkbox {
        width: 18px;
        height: 18px;
        border: 2px solid #ddd;
        border-radius: 3px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        position: relative;

        .checkmark {
          display: none;
        }
      }

      .option-label {
        flex: 1;
        font-size: 14px;
        color: #333;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .multi-select-dropdown {
    max-width: 100%;

    .multi-select-trigger {
      padding: 10px 12px;
      font-size: 13px;
    }

    .multi-select-dropdown-menu {
      .multi-select-option {
        padding: 10px 12px;
        font-size: 13px;
      }
    }
  }
} 