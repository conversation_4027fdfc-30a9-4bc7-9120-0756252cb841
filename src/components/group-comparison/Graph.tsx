import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer
} from 'recharts';
import './Graph.scss';

interface GroupData {
  id: string;
  name: string;
  consistency: number;
  trackCompletion: number;
  realAttendance: number;
  totalAttendance: number;
  submissionsCount: number;
  contestRating: number;
  realAttendancePercentage: number;
  totalAttendancePercentage: number;
  present: number;
  excused: number;
  absent: number;
  checkInOutRatio: number;
  averageConsistency: number;
  minimumConsistency: number;
  maximumConsistency: number;
  averageDailySolve: number;
  totalExpectedSolves: number;
  actualSolves: number;
}

interface GraphProps {
  data: GroupData[];
  activeTab: string;
}

const Graph: React.FC<GraphProps> = ({ data, activeTab }) => {
  // Transform data for chart
  const chartData = data.map(group => {
    const shortName = group.name.split(' ').pop()?.replace('Group', 'G') || group.id;
    
    return {
      name: shortName,
      'Real Attendance': group.realAttendance,
      'Consistency': group.consistency,
      'Track Completion': group.trackCompletion,
      'Total Attendance': group.totalAttendance,
      'Submissions Count': group.submissionsCount,
      'Contest Rating': group.contestRating,
      'Present': group.present,
      'Excused': group.excused,
      'Absent': group.absent,
      'Check In/Out Ratio': group.checkInOutRatio,
      'Average Consistency': group.averageConsistency,
      'Minimum Consistency': group.minimumConsistency,
      'Maximum Consistency': group.maximumConsistency,
      'Average Daily Solve': group.averageDailySolve,
      'Total Expected Solves': group.totalExpectedSolves,
      'Actual Solves': group.actualSolves,
    };
  });

  // Define colors for different metrics
  const colors = {
    'Real Attendance': '#059669', // Dark green
    'Consistency': '#10b981', // Light green
    'Track Completion': '#3b82f6', // Blue
    'Total Attendance': '#059669',
    'Submissions Count': '#8b5cf6',
    'Contest Rating': '#f59e0b',
    'Present': '#059669',
    'Excused': '#f59e0b',
    'Absent': '#ef4444',
    'Check In/Out Ratio': '#8b5cf6',
    'Average Consistency': '#10b981',
    'Minimum Consistency': '#f59e0b',
    'Maximum Consistency': '#059669',
    'Average Daily Solve': '#3b82f6',
    'Total Expected Solves': '#8b5cf6',
    'Actual Solves': '#10b981',
  };

  // Get columns based on active tab
  const getColumnsForTab = (tab: string) => {
    switch (tab) {
      case 'summary':
        return ['Real Attendance', 'Consistency', 'Track Completion'];
      case 'attendance':
        return ['Real Attendance', 'Total Attendance', 'Present', 'Excused', 'Absent'];
      case 'consistency':
        return ['Average Consistency', 'Minimum Consistency', 'Maximum Consistency', 'Average Daily Solve'];
      case 'trackCompletion':
        return ['Track Completion', 'Average Daily Solve', 'Total Expected Solves', 'Actual Solves'];
      case 'contestPerformance':
        return ['Contest Rating', 'Submissions Count'];
      default:
        return ['Real Attendance', 'Consistency', 'Track Completion'];
    }
  };

  const columns = getColumnsForTab(activeTab);

  return (
    <div className="graph-container">
      <ResponsiveContainer width="100%" height={400}>
        <BarChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
          <XAxis 
            dataKey="name" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6b7280' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#6b7280' }}
            domain={[0, 100]}
            ticks={[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]}
          />
          <Tooltip 
            contentStyle={{
              backgroundColor: 'white',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend 
            wrapperStyle={{
              paddingTop: '10px',
              fontSize: '12px'
            }}
          />
          {columns.map((column, index) => (
            <Bar
              key={column}
              dataKey={column}
              fill={colors[column as keyof typeof colors]}
              radius={[2, 2, 0, 0]}
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default Graph; 