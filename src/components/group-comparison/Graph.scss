.graph-container {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  // Custom tooltip styles
  .recharts-tooltip-wrapper {
    .recharts-default-tooltip {
      background-color: white !important;
      border: 1px solid #e5e7eb !important;
      border-radius: 8px !important;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
      padding: 0.75rem !important;
      
      .recharts-tooltip-label {
        color: #374151 !important;
        font-weight: 600 !important;
        margin-bottom: 0.5rem !important;
      }
      
      .recharts-tooltip-item {
        color: #6b7280 !important;
        font-size: 0.875rem !important;
        padding: 0.25rem 0 !important;
      }
    }
  }

  // Custom legend styles
  .recharts-legend-wrapper {
    .recharts-default-legend {
      .recharts-legend-item {
        .recharts-legend-item-text {
          color: #374151 !important;
          font-size: 0.875rem !important;
          font-weight: 500 !important;
        }
      }
    }
  }

  // Custom axis styles
  .recharts-cartesian-axis-tick-value {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
  }

  // Custom grid styles
  .recharts-cartesian-grid-horizontal line,
  .recharts-cartesian-grid-vertical line {
    stroke: #f3f4f6 !important;
    stroke-width: 1 !important;
  }

  // Bar styles
  .recharts-bar-rectangle {
    transition: opacity 0.2s ease;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .graph-container {
    padding: 1rem;
    
    .recharts-legend-wrapper {
      .recharts-default-legend {
        .recharts-legend-item {
          .recharts-legend-item-text {
            font-size: 0.75rem !important;
          }
        }
      }
    }
  }
} 