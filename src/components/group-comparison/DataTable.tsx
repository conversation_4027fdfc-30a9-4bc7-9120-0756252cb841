import React from 'react';
import './DataTable.scss';

interface DataTableProps<T> {
  data: T[];
  columns: Array<{
    key: keyof T;
    label: string;
    thresholds?: { green: number; yellow: number };
    reverse?: boolean;
    formatter?: (value: any) => React.ReactNode;
  }>;
  getRankDisplay?: (index: number) => React.ReactNode;
  getRowKey?: (item: T, index: number) => string;
  getRowName?: (item: T) => string;
}

// Utility function for highlighting
const getHighlightClass = (value: number, thresholds: { green: number; yellow: number }, reverse = false) => {
  if (reverse) {
    return value <= thresholds.green ? 'highlight-green' : value <= thresholds.yellow ? 'highlight-yellow' : 'highlight-red';
  }
  return value >= thresholds.green ? 'highlight-green' : value >= thresholds.yellow ? 'highlight-yellow' : 'highlight-red';
};

const DataTable = <T extends Record<string, any>>({ 
  data, 
  columns, 
  getRankDisplay,
  getRowKey = (item: T, index: number) => `row-${index}`,
  getRowName = (item: T) => (item as any).name || 'Unknown'
}: DataTableProps<T>) => (
  <div className="table-container">
    <table className="data-table">
      <thead>
        <tr>
          <th>Top Groups</th>
          {columns.map(column => (
            <th key={String(column.key)}>{column.label}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((item, index) => (
          <tr key={getRowKey(item, index)}>
            <td className="group-name">
              {getRankDisplay ? getRankDisplay(index) : <span className="rank-text">{index + 1}th</span>}
              {getRowName(item)}
            </td>
            {columns.map(column => {
              const value = item[column.key];
              const cellContent = column.formatter ? column.formatter(value) : value;
              const className = column.thresholds 
                ? getHighlightClass(value as number, column.thresholds, column.reverse)
                : '';
              
              return (
                <td key={String(column.key)} className={className}>
                  {cellContent}
                </td>
              );
            })}
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

export default DataTable; 