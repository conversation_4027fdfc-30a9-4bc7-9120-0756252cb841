"use client";

import Completion from "@/components/head-home/completion";
import GroupComparison from "@/components/group-comparison/GroupComparison";
import DailySolve from "@/components/head-home/dailySolve";
import ProblemSolvingChart from "@/components/head-home/ProblemSolvingChart";
import Summary from "@/components/head-home/summary";
import SubmissionTable from "@/components/SubmissionTable";
import { Submission } from "@/types/submission";
import LatestProblems from "@/components/LatestProblemsHomePage/LatestProblems";
import DailyQuoteCard from "@/components/common/DailyQuoteCard";
import EventsCard from "@/components/common/EventsCard";
import MiniReportCard from "@/components/common/MiniReportCard";

const submissions: Submission[] = [
    {
        id: 1,
        name: "<PERSON>",
        avatarUrl: "/avatar-fiona.jpg",
        problem: "Implement Trie (Prefix Tree)",
        timeSpent: 45,
        language: "Python",
        added: "2mo",
    },
    {
        id: 2,
        name: "<PERSON>",
        avatarUrl: "/avatar-fiona.jpg",
        problem: "Subarray Sum Equals K",
        timeSpent: 45,
        language: "Python",
        added: "2mo",
    },
    {
        id: 3,
        name: "<PERSON> Murugi",
        avatarUrl: "/avatar-fiona.jpg",
        problem: "Rabbits in Forest",
        timeSpent: 45,
        language: "Python",
        added: "2mo",
    },
    {
        id: 4,
        name: "Fiona Murugi",
        avatarUrl: "/avatar-fiona.jpg",
        problem: "Count the Number of Comple",
        timeSpent: 45,
        language: "Python",
        added: "2mo",
    },
    {
        id: 5,
        name: "Nathan Githinji Rugo",
        problem: "D - Swap Letters",
        timeSpent: 60,
        language: "Python",
        added: "3mo",
    },
    {
        id: 6,
        name: "Nathan Githinji Rugo",
        problem: "F - Good SubRectangle",
        timeSpent: 75,
        language: "Python",
        added: "3mo",
    },
];

export default function Home() {
    return (
        <div className="container mx-auto px-4 space-y-4 ">
            <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-2/3">
                    <DailyQuoteCard
                        quote="A wise man can learn more from a foolish question than a fool can learn from a wise answer."
                        author="Bruce Lee"
                        user="Biniyam"
                        role="HEAD"
                    />
                </div>
                <div className="flex-shrink-1">
                    <EventsCard
                        title="A2SV <> Remote G5F : Contest Analysis and Contest Up-solving"
                        date="Fri Jul 18"
                        time="08:00 - 11:30"
                        currentIndex={1}
                        totalEvents={2}
                        onPrevious={() => console.log("Previous")}
                        onNext={() => console.log("Next")}
                        targetDate={new Date("2024-07-18T08:00:00")}
                    />
                </div>
            </div>
            <div className="grid grid-cols-3 grid-rows-1 gap-4">
                <div>
                    <MiniReportCard
                        title="Solutions"
                        totalCount={1000}
                        currentValue={501}
                    />
                </div>
                <div>
                    <MiniReportCard
                        title="Time Spent"
                        totalCount={20000}
                        currentValue={10925}
                        unit="min"
                        customFormatting={true}
                    />
                </div>
                <div>
                    <MiniReportCard
                        title="Rating"
                        totalCount={2000}
                        currentValue={1636}
                        icon="/icons/bar-home.svg"
                        showChart={true}
                    />
                </div>
            </div>

            <div className=" md:flex  md:space-y-0 mb-4  md:gap-4 ">
                <DailySolve />
                <ProblemSolvingChart />
            </div>
            <Summary />
            <SubmissionTable submissions={submissions} />
            <GroupComparison />
            <Completion />
            <LatestProblems />
            <LatestProblems />
        </div>
    );
}
