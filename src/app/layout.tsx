import type { Metada<PERSON> } from "next";
import { Providers } from "../store/Providers";
import "../styles/globals.css";
import Sidebar from "@/components/common/sidebar";
import Topbar from "@/components/common/topbar";

export const metadata: Metadata = {
  title: "A2SV Hub",
  description: "A2SV Hub - Empowering African Students in Tech",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="flex h-screen ">
        <Providers>
          <Sidebar role="head" />
          <div className="flex flex-col  w-full">
            <Topbar />
            <main className="p-4 overflow-y-auto flex-1">{children}</main>
          </div>
        </Providers>
      </body>
    </html>
  );
}
